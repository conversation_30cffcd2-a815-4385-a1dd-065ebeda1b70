// User Data
const userData = {
    profile: {
        name: "Alice Wonderland",
        customerAccount: "ACC12345",
        phone: "************",
        email: "<EMAIL>",
        fax: "************",
        currency: "CAD",
        country: "Canada",
        state: "British Columbia",
        region: "West",
        location: "Vancouver",
        mobile: "************",
        type: "Customer",
        creditLimit: 100000,
        paymentDueDays: 30,
        isActive: true,
        isDealer: false,
        company: "Wonderland Corp",
        branch: "Main Branch",
        customerType: "Corporate",
        language: "English",
        variancePercent: 5,
        varianceValue: 500,
        paymentTerms: "Net 30 days from invoice date.",
        isPoMandatory: true,
        profilePicture: "https://placehold.co/120x120/cccccc/333333?text=User"
    },
    credit: [],
    contacts: [
        {id: 1, name: "<PERSON> Builder", dob: "1980-05-10", department: "Construction", email: "<EMAIL>", phone: "************", mobile: "************", remarks: "Handles large accounts and manages major building projects efficiently.", isActive: true, isDefaultContact: false},
        {id: 2, name: "<PERSON>", dob: "1889-04-16", department: "Arts & Entertainment", email: "<EMAIL>", phone: "************", mobile: "************", remarks: "Specializes in visual storytelling. Known for silent films and comedic genius.", isActive: true, isDefaultContact: true},
        {id: 3, name: "<PERSON> Prince", dob: "1941-10-21", department: "Justice & Security", email: "<EMAIL>", phone: "************", mobile: "************", remarks: "Oversees critical operations and international relations with a focus on peace.", isActive: false, isDefaultContact: false},
        {id: 4, name: "Eve Harrington", dob: "1975-03-22", department: "Human Resources", email: "<EMAIL>", phone: "************", mobile: "************", remarks: "Manages employee benefits, recruitment, and onboarding processes for new hires.", isActive: true, isDefaultContact: false},
        {id: 5, name: "Frank Sinatra", dob: "1915-12-12", department: "Finance", email: "<EMAIL>", phone: "************", mobile: "************", remarks: "Provides market insights and investment strategies for high-net-worth clients.", isActive: false, isDefaultContact: false}
    ],
    addresses: [
        {id: 1, addressLine1: "123 Main St", addressLine2: "Apt 101", city: "Anytown", district: "Central", stateProvince: "CA", postalCode: "90210", country: "USA", isActive: true, isDefault: true},
        {id: 2, addressLine1: "456 Oak Ave", addressLine2: "", city: "Big City", district: "Downtown", stateProvince: "NY", postalCode: "10001", country: "USA", isActive: true, isDefault: false},
        {id: 3, addressLine1: "789 Pine Ln", addressLine2: "Unit 5", city: "Smallville", district: "Eastside", stateProvince: "ON", postalCode: "M1M 1M1", country: "Canada", isActive: false, isDefault: false}
    ],
    taxes: [
        {id: 1, code: "GST", description: "Goods and Services Tax", isActive: true},
        {id: 2, code: "PST", description: "Provincial Sales Tax", isActive: true},
        {id: 3, code: "HST", description: "Harmonized Sales Tax", isActive: false},
        {id: 4, code: "VAT", description: "Value Added Tax", isActive: true}
    ],
    segments: [
        {id: 1, primarySegment: "Commercial", secondarySegment: "Heavy Duty", isActive: true},
        {id: 2, primarySegment: "Personal", secondarySegment: "Luxury", isActive: true},
        {id: 3, primarySegment: "Fleet", secondarySegment: "Logistics", isActive: false}
    ],
    serviceSchedule: [
        {id: 1, serviceType: "Oil Change", serviceDate: "2025-08-15", workOrderNum: "WO-001", status: "Scheduled", closureReason: ""},
        {id: 2, serviceType: "Tire Rotation", serviceDate: "2025-07-20", workOrderNum: "WO-002", status: "Completed", closureReason: "Routine maintenance completed."},
        {id: 3, serviceType: "Brake Inspection", serviceDate: "2025-09-01", workOrderNum: "WO-003", status: "Scheduled", closureReason: ""}
    ],
    vehicleDetails: [
        {id: 1, brand: "Volvo", model: "VNL 860", vehicleType: "Truck", vin: "123ABCDEF45678901"},
        {id: 2, brand: "Prevost", model: "H3-45", vehicleType: "Coach", vin: "ABC9876543210FEDC"},
        {id: 3, brand: "Mack", model: "Anthem", vehicleType: "Truck", vin: "DEF11223344556677"}
    ],
    contractors: [
        {id: 1, agreement: "Agreement 2024-001", fromDate: "2024-01-01", toDate: "2024-12-31", contractValue: 50000, unit: "Per Project", currency: "USD", remarks: "Software development project for mobile app."},
        {id: 2, agreement: "Maintenance Contract Q3", fromDate: "2024-07-01", toDate: "2024-09-30", contractValue: 15000, unit: "Monthly", currency: "CAD", remarks: "Quarterly infrastructure maintenance."},
        {id: 3, agreement: "Consulting Services", fromDate: "2025-02-10", toDate: "2025-05-10", contractValue: 7500, unit: "Hourly", currency: "INR", remarks: "Advisory services for market expansion."}
    ],
    taxStructures: [
        {id: 1, name: "GST Canada (5%)", isActive: true},
        {id: 2, name: "VAT Europe (20%)", isActive: true},
        {id: 3, name: "Sales Tax US (7%)", isActive: false}
    ],
    customerDiscounts: [
        {id: 1, partDiscount: 10.00, serviceDiscount: 5.00, effectiveDate: "2024-01-01"},
        {id: 2, partDiscount: 15.00, serviceDiscount: 10.00, effectiveDate: "2024-07-15"},
        {id: 3, partDiscount: 5.00, serviceDiscount: 2.50, effectiveDate: "2025-01-01"}
    ],
    customerRateContracts: [
        {id: 1, effectiveFrom: "2024-01-01", effectiveTo: "2024-12-31", uploadedBy: "John Doe", uploadedDate: "2023-12-20"},
        {id: 2, effectiveFrom: "2024-07-01", effectiveTo: "2025-06-30", uploadedBy: "Jane Smith", uploadedDate: "2024-06-10"},
        {id: 3, effectiveFrom: "2025-01-01", effectiveTo: "2025-06-30", uploadedBy: "Admin User", uploadedDate: "2024-12-01"}
    ]
};

// Global Variables
let isEditMode = false;
let currentView = 'card';

// Initialize Application
$(document).ready(function() {
    initializeApp();
    loadProfileData();
    setupEventListeners();
});

function initializeApp() {
    // Show profile section by default
    showSection('profile');
}

function setupEventListeners() {
    // Sidebar navigation
    $('.list-group-item').click(function() {
        const section = $(this).data('section');
        $('.list-group-item').removeClass('active');
        $(this).addClass('active');
        showSection(section);
    });

    // Mode toggle
    $('#viewModeBtn').click(() => toggleMode(false));
    $('#editModeBtn').click(() => toggleMode(true));

    // View toggle
    $('.view-toggle').click(function() {
        const view = $(this).data('view');
        const target = $(this).data('target');
        $('.view-toggle').removeClass('active');
        $(this).addClass('active');
        currentView = view;
        if (target) {
            renderSection(target);
        }
    });
}

function showSection(sectionName) {
    $('.section').removeClass('active');
    $(`#${sectionName}-section`).addClass('active');
    
    // Load section data
    switch(sectionName) {
        case 'profile':
            loadProfileData();
            break;
        case 'contacts':
            renderContacts();
            break;
        case 'addresses':
            renderAddresses();
            break;
        case 'vehicles':
            renderVehicles();
            break;
        case 'service':
            renderService();
            break;
        case 'contracts':
            renderContracts();
            break;
        case 'financial':
            renderFinancial();
            break;
    }
}

function loadProfileData() {
    const profile = userData.profile;
    
    $('#profilePicture').attr('src', profile.profilePicture);
    $('#customerName').text(profile.name);
    $('#customerType').text(profile.customerType);
    $('#email').text(profile.email);
    $('#phone').text(profile.phone);
    $('#mobile').text(profile.mobile);
    $('#customerAccount').text(profile.customerAccount);
    $('#company').text(profile.company);
    $('#creditLimit').text(`$${profile.creditLimit.toLocaleString()} ${profile.currency}`);
    
    // Set edit field values
    $('#email-edit').val(profile.email);
    $('#phone-edit').val(profile.phone);
    $('#company-edit').val(profile.company);
    $('#creditLimit-edit').val(profile.creditLimit);
}

function toggleMode(editMode) {
    isEditMode = editMode;
    
    if (editMode) {
        $('#viewModeBtn').removeClass('btn-outline-light').addClass('btn-outline-secondary');
        $('#editModeBtn').removeClass('btn-outline-light').addClass('btn-light');
        $('.edit-field').show();
        $('span:not(.navbar-brand span)').hide();
    } else {
        $('#editModeBtn').removeClass('btn-light').addClass('btn-outline-light');
        $('#viewModeBtn').removeClass('btn-outline-secondary').addClass('btn-outline-light');
        $('.edit-field').hide();
        $('span:not(.navbar-brand span)').show();
    }
}

function renderContacts() {
    const container = $('#contacts-container');
    container.empty();
    
    if (currentView === 'card') {
        renderContactsCard(container);
    } else if (currentView === 'list') {
        renderContactsList(container);
    } else if (currentView === 'grid') {
        renderContactsGrid(container);
    } else if (currentView === 'compact') {
        renderContactsCompact(container);
    }
}

function renderContactsCard(container) {
    const row = $('<div class="row g-3 card-view"></div>');
    
    userData.contacts.forEach(contact => {
        const statusBadge = contact.isActive ? 
            '<span class="badge status-active">Active</span>' : 
            '<span class="badge status-inactive">Inactive</span>';
        
        const defaultBadge = contact.isDefaultContact ? 
            '<span class="badge bg-warning text-dark ms-1">Default</span>' : '';
        
        const card = $(`
            <div class="col-md-6 col-lg-4">
                <div class="card bg-dark border-secondary h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="card-title text-white">${contact.name}</h6>
                            <div>${statusBadge}${defaultBadge}</div>
                        </div>
                        <p class="card-text text-muted small">${contact.department}</p>
                        <div class="contact-details">
                            <small class="text-muted d-block"><i class="fas fa-envelope me-1"></i>${contact.email}</small>
                            <small class="text-muted d-block"><i class="fas fa-phone me-1"></i>${contact.phone}</small>
                            <small class="text-muted d-block"><i class="fas fa-mobile-alt me-1"></i>${contact.mobile}</small>
                        </div>
                        <p class="card-text mt-2"><small class="text-muted">${contact.remarks}</small></p>
                    </div>
                </div>
            </div>
        `);
        row.append(card);
    });
    
    container.append(row);
}

function renderContactsList(container) {
    const listContainer = $('<div class="list-view"></div>');

    userData.contacts.forEach(contact => {
        const statusBadge = contact.isActive ?
            '<span class="badge status-active">Active</span>' :
            '<span class="badge status-inactive">Inactive</span>';

        const listItem = $(`
            <div class="list-item">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <h6 class="mb-1">${contact.name}</h6>
                        <small class="text-muted">${contact.department}</small>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted d-block">${contact.email}</small>
                        <small class="text-muted d-block">${contact.phone}</small>
                    </div>
                    <div class="col-md-4">
                        <small class="text-muted">${contact.remarks}</small>
                    </div>
                    <div class="col-md-2 text-end">
                        ${statusBadge}
                    </div>
                </div>
            </div>
        `);
        listContainer.append(listItem);
    });

    container.append(listContainer);
}

function renderContactsGrid(container) {
    const gridContainer = $('<div class="grid-view"></div>');

    userData.contacts.forEach(contact => {
        const statusBadge = contact.isActive ?
            '<span class="badge status-active">Active</span>' :
            '<span class="badge status-inactive">Inactive</span>';

        const gridItem = $(`
            <div class="grid-item">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h6 class="text-white">${contact.name}</h6>
                    ${statusBadge}
                </div>
                <p class="text-muted small mb-2">${contact.department}</p>
                <div class="contact-info">
                    <small class="text-muted d-block mb-1"><i class="fas fa-envelope me-1"></i>${contact.email}</small>
                    <small class="text-muted d-block mb-1"><i class="fas fa-phone me-1"></i>${contact.phone}</small>
                </div>
            </div>
        `);
        gridContainer.append(gridItem);
    });

    container.append(gridContainer);
}

function renderContactsCompact(container) {
    const compactContainer = $('<div class="compact-view"></div>');

    userData.contacts.forEach(contact => {
        const statusIcon = contact.isActive ?
            '<i class="fas fa-circle text-success"></i>' :
            '<i class="fas fa-circle text-secondary"></i>';

        const compactItem = $(`
            <div class="compact-item">
                <div>
                    <strong>${contact.name}</strong> - ${contact.department}
                    <small class="text-muted d-block">${contact.email} | ${contact.phone}</small>
                </div>
                <div>${statusIcon}</div>
            </div>
        `);
        compactContainer.append(compactItem);
    });

    container.append(compactContainer);
}

function renderSection(sectionName) {
    switch(sectionName) {
        case 'contacts':
            renderContacts();
            break;
        case 'addresses':
            renderAddresses();
            break;
        case 'vehicles':
            renderVehicles();
            break;
        case 'service':
            renderService();
            break;
        case 'contracts':
            renderContracts();
            break;
        case 'financial':
            renderFinancial();
            break;
    }
}

function renderAddresses() {
    const container = $('#addresses-container');
    container.empty();

    if (currentView === 'card') {
        const row = $('<div class="row g-3 card-view"></div>');
        userData.addresses.forEach(address => {
            const statusBadge = address.isActive ?
                '<span class="badge status-active">Active</span>' :
                '<span class="badge status-inactive">Inactive</span>';
            const defaultBadge = address.isDefault ?
                '<span class="badge bg-warning text-dark ms-1">Default</span>' : '';

            const card = $(`
                <div class="col-md-6 col-lg-4">
                    <div class="card bg-dark border-secondary h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="card-title text-white">Address ${address.id}</h6>
                                <div>${statusBadge}${defaultBadge}</div>
                            </div>
                            <div class="address-details">
                                <p class="mb-1">${address.addressLine1}</p>
                                ${address.addressLine2 ? `<p class="mb-1">${address.addressLine2}</p>` : ''}
                                <p class="mb-1">${address.city}, ${address.stateProvince} ${address.postalCode}</p>
                                <p class="mb-0 text-muted">${address.country}</p>
                            </div>
                        </div>
                    </div>
                </div>
            `);
            row.append(card);
        });
        container.append(row);
    }
}

function renderVehicles() {
    const container = $('#vehicles-container');
    container.empty();

    if (currentView === 'card') {
        const row = $('<div class="row g-3 card-view"></div>');
        userData.vehicleDetails.forEach(vehicle => {
            const card = $(`
                <div class="col-md-6 col-lg-4">
                    <div class="card bg-dark border-secondary h-100">
                        <div class="card-body">
                            <h6 class="card-title text-white">${vehicle.brand} ${vehicle.model}</h6>
                            <div class="vehicle-details">
                                <p class="mb-1"><strong>Type:</strong> ${vehicle.vehicleType}</p>
                                <p class="mb-1"><strong>VIN:</strong> <small class="text-muted">${vehicle.vin}</small></p>
                            </div>
                        </div>
                    </div>
                </div>
            `);
            row.append(card);
        });
        container.append(row);
    }
}

function renderService() {
    const container = $('#service-container');
    container.empty();

    if (currentView === 'card') {
        const row = $('<div class="row g-3 card-view"></div>');
        userData.serviceSchedule.forEach(service => {
            const statusBadge = service.status === 'Completed' ?
                '<span class="badge status-completed">Completed</span>' :
                '<span class="badge status-scheduled">Scheduled</span>';

            const card = $(`
                <div class="col-md-6 col-lg-4">
                    <div class="card bg-dark border-secondary h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="card-title text-white">${service.serviceType}</h6>
                                ${statusBadge}
                            </div>
                            <div class="service-details">
                                <p class="mb-1"><strong>Date:</strong> ${service.serviceDate}</p>
                                <p class="mb-1"><strong>Work Order:</strong> ${service.workOrderNum}</p>
                                ${service.closureReason ? `<p class="mb-0 text-muted small">${service.closureReason}</p>` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            `);
            row.append(card);
        });
        container.append(row);
    }
}

function renderContracts() {
    const container = $('#contracts-container');
    container.empty();

    if (currentView === 'card') {
        const row = $('<div class="row g-3 card-view"></div>');
        userData.contractors.forEach(contract => {
            const card = $(`
                <div class="col-md-6 col-lg-4">
                    <div class="card bg-dark border-secondary h-100">
                        <div class="card-body">
                            <h6 class="card-title text-white">${contract.agreement}</h6>
                            <div class="contract-details">
                                <p class="mb-1"><strong>Period:</strong> ${contract.fromDate} to ${contract.toDate}</p>
                                <p class="mb-1"><strong>Value:</strong> ${contract.contractValue.toLocaleString()} ${contract.currency} (${contract.unit})</p>
                                <p class="mb-0 text-muted small">${contract.remarks}</p>
                            </div>
                        </div>
                    </div>
                </div>
            `);
            row.append(card);
        });
        container.append(row);
    }
}

function renderFinancial() {
    const container = $('#financial-container');
    container.empty();

    const tabsHtml = `
        <div class="row g-3">
            <div class="col-md-6">
                <div class="card bg-dark border-secondary">
                    <div class="card-header">
                        <h6 class="mb-0">Tax Structures</h6>
                    </div>
                    <div class="card-body">
                        ${userData.taxStructures.map(tax => `
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>${tax.name}</span>
                                <span class="badge ${tax.isActive ? 'status-active' : 'status-inactive'}">${tax.isActive ? 'Active' : 'Inactive'}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card bg-dark border-secondary">
                    <div class="card-header">
                        <h6 class="mb-0">Customer Discounts</h6>
                    </div>
                    <div class="card-body">
                        ${userData.customerDiscounts.map(discount => `
                            <div class="mb-2">
                                <div><strong>Parts:</strong> ${discount.partDiscount}% | <strong>Service:</strong> ${discount.serviceDiscount}%</div>
                                <small class="text-muted">Effective: ${discount.effectiveDate}</small>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        </div>
    `;

    container.append(tabsHtml);
}
